#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
32-bit 仿射映射： y = M · x  ⊕  C
兼容 Python 2.7 / 3.x（无额外依赖）
"""

# ---------------- 常量 ----------------
M_ROWS = [
    0x00000004, 0x00000008, 0x00000010, 0x00000023,
    0x00000046, 0x0000008C, 0x00000118, 0x00000230,
    0x00000460, 0x000008C0, 0x00001180, 0x00002300,
    0x00004600, 0x00008C00, 0x00011800, 0x00023000,
    0x00046000, 0x0008C000, 0x00118000, 0x00230000,
    0x00460000, 0x008C0000, 0x01180000, 0x02300000,
    0x04600000, 0x08C00000, 0x11800000, 0x23000000,
    0x46000000, 0x8C000000, 0x18000000, 0x30000000,
]
C = 0x6500001D

# ---------------- 奇偶函数 ----------------
# Python ≥3.8 有 int.bit_count()；旧版本 fallback 到 bin(v).count("1")
try:
    _ = (1).bit_count        # 测试一下有没有此方法

    def _parity(v):
        return (v & 0xFFFFFFFF).bit_count() & 1

except AttributeError:
    def _parity(v):
        return bin(v & 0xFFFFFFFF).count("1") & 1

# ---------------- 主变换 -----------------
def transform(x):
    """
    :param x:  32-bit int (0–0xFFFFFFFF)
    :return:   32-bit int
    """
    y = C
    for i, mask in enumerate(M_ROWS):
        if _parity(x & mask):
            y ^= 1 << i
    return y & 0xFFFFFFFF

# ---------------- CLI -------------------
if __name__ == "__main__":
    import sys, textwrap
    if len(sys.argv) < 2:
        print(textwrap.dedent(f"""
            Usage:
                python {sys.argv[0]} <value>

            <value> 支持十进制或 0x 前缀十六进制。
            例：  python {sys.argv[0]} 0xd22a86af
        """).strip())
        sys.exit(0)

    x_in = int(sys.argv[1], 0)
    print(hex(transform(x_in)))
