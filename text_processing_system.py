"""
文本处理和语言处理系统 - 合并版本
包含所有7个核心模块的完整实现

这是一个综合性的文本处理和语言处理系统，展示了从基础数据结构到高级NLP技术的完整实现。
所有模块都集成在单个文件中，便于部署和使用。

模块列表：
1. 文本收集器 (TextCollector) - 从英文新闻网站收集文本数据
   - 支持从BBC等新闻网站抓取文本
   - 提供示例文本作为备选
   - 自动文本清理和预处理

2. 固定搭配查找器 (CollocationFinder) - 使用多种数据结构查找固定搭配
   - 哈希表：快速存储和查找n-gram频率
   - 前缀树(Trie)：高效的模式匹配
   - 排序数组：按频率排序的固定搭配

3. 基础反向索引 (BasicInvertedIndex) - 支持多单词搜索
   - 构建词到文档的倒排索引
   - 支持AND、OR、短语搜索
   - TF-IDF分数计算和排序

4. 词法分析反向索引 (LexicalInvertedIndex) - 考虑词法分析的搜索
   - 词性标注(POS tagging)
   - 词形还原(Lemmatization)
   - 按词性和词形搜索

5. 词向量反向索引 (VectorInvertedIndex) - 基于词向量的语义搜索
   - 简化词向量模型实现
   - 语义相似性搜索
   - 查询扩展和混合搜索

6. 正则表达式DFA (RegexDFA) - 正则表达式到确定有限自动机
   - NFA到DFA的转换算法
   - 支持基本正则表达式操作
   - 字符串匹配验证

7. 简单编程语言解释器 (SimpleInterpreter) - 支持基本编程功能
   - 词法分析器(Lexer)
   - 语法分析器(Parser)
   - 解释器(Interpreter)
   - 支持变量、运算、条件、循环等

技术特点：
- 模块化设计，每个功能独立实现
- 容错性强，依赖库不可用时自动切换到简化实现
- 完整的算法实现，不依赖外部API
- 适合学习和研究文本处理技术
"""

import re
import requests
import numpy as np
import math
from collections import defaultdict, Counter
from typing import List, Dict, Set, Tuple, Optional, Any
from enum import Enum
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer

# 尝试导入NLTK
try:
    import nltk
    try:
        nltk.data.find('tokenizers/punkt')
        from nltk.tokenize import word_tokenize
        from nltk.tag import pos_tag
        from nltk.stem import WordNetLemmatizer
        from nltk.corpus import stopwords, wordnet
        NLTK_AVAILABLE = True
    except LookupError:
        print("下载NLTK数据...")
        try:
            nltk.download('punkt', quiet=True)
            nltk.download('averaged_perceptron_tagger', quiet=True)
            nltk.download('wordnet', quiet=True)
            nltk.download('stopwords', quiet=True)
            from nltk.tokenize import word_tokenize
            from nltk.tag import pos_tag
            from nltk.stem import WordNetLemmatizer
            from nltk.corpus import stopwords, wordnet
            NLTK_AVAILABLE = True
        except:
            print("NLTK数据下载失败，使用简化实现...")
            NLTK_AVAILABLE = False
except ImportError:
    print("NLTK未安装，使用简化实现...")
    NLTK_AVAILABLE = False

# 尝试导入BeautifulSoup
try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    print("BeautifulSoup4未安装，网页抓取功能将不可用...")
    BS4_AVAILABLE = False

# =============================================================================
# 模块1: 文本收集器 (TextCollector)
# =============================================================================

class TextCollector:
    """
    文本收集模块 - 从英文新闻网站收集文本数据

    功能说明：
    1. 从BBC等新闻网站抓取文章文本
    2. 提供示例文本作为备选方案
    3. 自动清理和预处理文本内容
    4. 控制文本长度在合理范围内

    主要方法：
    - collect_texts(): 主要收集方法，支持多种来源
    - collect_from_bbc(): 从BBC新闻收集文本
    - save_texts(): 保存收集的文本到文件

    技术特点：
    - 使用BeautifulSoup解析HTML
    - 模拟浏览器请求头避免被屏蔽
    - 容错处理，网络不可用时使用示例文本
    - 自动文本清理和长度控制
    """

    def __init__(self):
        """
        初始化文本收集器

        设置：
        - headers: 模拟浏览器的请求头，避免被网站屏蔽
        - collected_texts: 存储收集到的文本列表
        """
        # 设置HTTP请求头，模拟真实浏览器访问
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        # 存储收集到的文本
        self.collected_texts = []
    
    def collect_from_bbc(self, num_articles: int = 3) -> List[str]:
        """
        从BBC新闻网站收集文本

        参数:
            num_articles (int): 要收集的文章数量，默认3篇

        返回:
            List[str]: 收集到的文本列表

        工作流程:
        1. 检查BeautifulSoup4是否可用
        2. 访问BBC新闻主页
        3. 提取文章链接
        4. 逐个访问文章页面提取文本
        5. 过滤掉过短的文本（少于50个单词）
        6. 如果失败则返回示例文本
        """
        # 检查依赖库是否可用
        if not BS4_AVAILABLE:
            print("BeautifulSoup4不可用，使用示例文本...")
            return self._get_sample_texts()

        print("正在从BBC News收集文本...")
        url = "https://www.bbc.com/news"

        try:
            # 发送HTTP请求获取BBC新闻主页
            response = requests.get(url, headers=self.headers, timeout=10)
            soup = BeautifulSoup(response.content, 'html.parser')

            # 提取文章链接
            article_links = []
            for link in soup.find_all('a', href=True):
                href = link['href']
                # 筛选新闻文章链接
                if '/news/' in href and href.startswith('/news/'):
                    full_url = f"https://www.bbc.com{href}"
                    article_links.append(full_url)
                    # 达到所需数量就停止
                    if len(article_links) >= num_articles:
                        break

            # 逐个提取文章文本
            texts = []
            for i, article_url in enumerate(article_links[:num_articles]):
                print(f"收集第 {i+1} 篇文章...")
                text = self._extract_article_text(article_url)
                # 只保留足够长的文本（至少50个单词）
                if text and len(text.split()) >= 50:
                    texts.append(text)

            # 如果成功收集到文本就返回，否则返回示例文本
            return texts if texts else self._get_sample_texts()

        except Exception as e:
            print(f"从BBC收集文本时出错: {e}")
            return self._get_sample_texts()
    
    def _extract_article_text(self, url: str) -> str:
        """从单篇文章提取文本"""
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            content_selectors = [
                '[data-component="text-block"]',
                '.story-body__inner p',
                'article p',
                '.post-content p',
                'p'
            ]
            
            text_parts = []
            for selector in content_selectors:
                elements = soup.select(selector)
                if elements:
                    for elem in elements:
                        text = elem.get_text().strip()
                        if len(text) > 20:
                            text_parts.append(text)
                    break
            
            full_text = ' '.join(text_parts)
            full_text = re.sub(r'\s+', ' ', full_text)
            full_text = re.sub(r'[^\w\s.,!?;:\-\'"()]', '', full_text)
            
            words = full_text.split()
            if len(words) > 400:
                words = words[:400]
            
            return ' '.join(words)
            
        except Exception as e:
            print(f"提取文章文本时出错: {e}")
            return ""
    
    def _get_sample_texts(self) -> List[str]:
        """提供示例文本"""
        print("使用示例文本...")
        return [
            """
            The global economy continues to face unprecedented challenges as inflation rates 
            soar across major economies. Central banks worldwide are implementing aggressive 
            monetary policies to combat rising prices, but economists warn that these measures 
            could lead to recession. The Federal Reserve has raised interest rates multiple 
            times this year, while the European Central Bank follows suit with similar 
            strategies. Supply chain disruptions, energy costs, and geopolitical tensions 
            contribute to the complex economic landscape. Businesses struggle to maintain 
            profitability while consumers face increased living costs. Market volatility 
            reflects uncertainty about future economic conditions. Governments consider 
            fiscal interventions to support vulnerable populations and maintain economic 
            stability. International cooperation becomes crucial for addressing these 
            global challenges effectively.
            """,
            """
            Artificial intelligence technology advances rapidly, transforming industries 
            and reshaping the future of work. Machine learning algorithms demonstrate 
            remarkable capabilities in natural language processing, computer vision, and 
            decision-making tasks. Companies invest heavily in AI research and development, 
            seeking competitive advantages through automation and intelligent systems. 
            However, concerns about job displacement and ethical implications grow among 
            policymakers and society. Experts debate the need for regulation and governance 
            frameworks to ensure responsible AI development. Educational institutions 
            adapt curricula to prepare students for an AI-driven economy. Healthcare, 
            finance, and transportation sectors experience significant AI integration. 
            The technology promises improved efficiency and innovation while raising 
            questions about privacy, bias, and human autonomy in decision-making processes.
            """,
            """
            Climate change impacts accelerate globally, prompting urgent action from 
            governments and organizations worldwide. Rising temperatures, extreme weather 
            events, and sea-level increases threaten ecosystems and human communities. 
            Renewable energy adoption grows as countries commit to carbon neutrality goals. 
            Solar and wind power technologies become increasingly cost-effective alternatives 
            to fossil fuels. Environmental activists advocate for stronger climate policies 
            and corporate accountability. Scientists warn that immediate action is necessary 
            to prevent catastrophic consequences. International agreements like the Paris 
            Climate Accord guide global cooperation efforts. Green technology innovation 
            accelerates, creating new economic opportunities while addressing environmental 
            challenges. Public awareness and individual behavior changes contribute to 
            collective climate action initiatives across diverse communities and regions.
            """
        ]
    
    def collect_texts(self, source: str = "sample", num_articles: int = 3) -> List[str]:
        """收集文本的主要方法"""
        if source == "bbc":
            texts = self.collect_from_bbc(num_articles)
        else:
            texts = self._get_sample_texts()
        
        self.collected_texts = texts
        print(f"成功收集了 {len(texts)} 篇文本")
        for i, text in enumerate(texts):
            word_count = len(text.split())
            print(f"文本 {i+1}: {word_count} 个单词")
        
        return texts
    
    def save_texts(self, filename: str = "collected_texts.txt"):
        """保存收集的文本到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            for i, text in enumerate(self.collected_texts):
                f.write(f"=== 文本 {i+1} ===\n")
                f.write(text.strip())
                f.write("\n\n")
        print(f"文本已保存到 {filename}")

# =============================================================================
# 模块2: 固定搭配查找器 (CollocationFinder)
# =============================================================================

class TrieNode:
    """
    前缀树节点类

    属性说明：
    - children: 子节点字典，键为单词，值为TrieNode
    - is_end: 标记是否为短语结束节点
    - frequency: 该短语的出现频率
    - phrases: 存储以该节点结尾的完整短语列表
    """
    def __init__(self):
        self.children = {}      # 子节点字典
        self.is_end = False     # 是否为短语结束
        self.frequency = 0      # 短语频率
        self.phrases = []       # 短语列表

class Trie:
    """
    前缀树(Trie)实现类

    功能说明：
    前缀树是一种树形数据结构，用于高效存储和检索字符串集合。
    在固定搭配查找中，用于存储和快速匹配多词短语。

    优势：
    - 空间效率高：共享公共前缀
    - 查找效率高：O(m)时间复杂度，m为短语长度
    - 支持前缀匹配和模式搜索
    """
    def __init__(self):
        """初始化前缀树，创建根节点"""
        self.root = TrieNode()

    def insert(self, phrase: str, frequency: int = 1):
        """
        插入短语到前缀树

        参数:
            phrase (str): 要插入的短语
            frequency (int): 短语出现频率

        算法流程:
        1. 从根节点开始
        2. 按单词逐级向下遍历/创建节点
        3. 在结束节点标记短语信息
        """
        node = self.root
        words = phrase.lower().split()

        # 逐词遍历，创建或移动到子节点
        for word in words:
            if word not in node.children:
                node.children[word] = TrieNode()
            node = node.children[word]

        # 标记短语结束并记录信息
        node.is_end = True
        node.frequency += frequency
        if phrase not in node.phrases:
            node.phrases.append(phrase)
    
    def search(self, phrase: str) -> bool:
        """搜索短语是否存在"""
        node = self.root
        words = phrase.lower().split()
        
        for word in words:
            if word not in node.children:
                return False
            node = node.children[word]
        
        return node.is_end
    
    def get_frequency(self, phrase: str) -> int:
        """获取短语频率"""
        node = self.root
        words = phrase.lower().split()
        
        for word in words:
            if word not in node.children:
                return 0
            node = node.children[word]
        
        return node.frequency if node.is_end else 0

class SortedArray:
    """排序数组实现"""
    def __init__(self):
        self.data = []
    
    def insert(self, phrase: str, frequency: int):
        """插入短语和频率，保持排序"""
        item = (frequency, phrase)
        left, right = 0, len(self.data)
        while left < right:
            mid = (left + right) // 2
            if self.data[mid][0] < frequency:
                left = mid + 1
            else:
                right = mid
        self.data.insert(left, item)
    
    def get_top_k(self, k: int) -> List[Tuple[int, str]]:
        """获取频率最高的k个短语"""
        return self.data[-k:] if k <= len(self.data) else self.data[:]
    
    def search(self, phrase: str) -> int:
        """搜索短语频率"""
        for freq, p in self.data:
            if p == phrase:
                return freq
        return 0

class CollocationFinder:
    """
    固定搭配查找器 - 使用多种数据结构查找文本中的固定搭配

    功能说明：
    固定搭配(Collocation)是指经常一起出现的词语组合，如"artificial intelligence"、
    "climate change"等。本类使用三种不同的数据结构来存储和查找固定搭配：

    1. 哈希表(Hash Table): 提供O(1)的查找时间复杂度
    2. 前缀树(Trie): 支持前缀匹配和模式搜索
    3. 排序数组(Sorted Array): 按频率排序，便于获取高频搭配

    支持的n-gram类型：
    - 二元组合(Bigrams): 两个词的组合
    - 三元组合(Trigrams): 三个词的组合
    - 四元组合(Fourgrams): 四个词的组合

    主要方法：
    - analyze_texts(): 分析文本提取固定搭配
    - find_collocations_hash(): 使用哈希表查找
    - find_collocations_trie(): 使用前缀树查找
    - get_top_collocations_sorted(): 获取高频搭配
    """
    def __init__(self):
        """
        初始化固定搭配查找器

        数据结构说明：
        - hash_table: 哈希表，存储短语->频率的映射
        - trie: 前缀树，用于模式匹配
        - sorted_array: 排序数组，按频率排序
        - texts: 原始文本列表
        - bigrams/trigrams/fourgrams: 分别存储2/3/4元组合的计数器
        """
        self.hash_table = defaultdict(int)  # 哈希表存储
        self.trie = Trie()                  # 前缀树存储
        self.sorted_array = SortedArray()   # 排序数组存储
        self.texts = []                     # 原始文本
        self.bigrams = Counter()            # 二元组合计数器
        self.trigrams = Counter()           # 三元组合计数器
        self.fourgrams = Counter()          # 四元组合计数器

    def preprocess_text(self, text: str) -> List[str]:
        """
        预处理文本，清理和标准化

        参数:
            text (str): 原始文本

        返回:
            List[str]: 清理后的单词列表

        处理步骤:
        1. 转换为小写
        2. 移除标点符号，只保留字母和空格
        3. 分割成单词并过滤空字符串
        """
        # 移除标点符号，转为小写
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        # 分割并过滤空字符串
        words = [word for word in text.split() if word.strip()]
        return words

    def extract_ngrams(self, words: List[str], n: int) -> List[str]:
        """
        从单词列表中提取n-gram组合

        参数:
            words (List[str]): 单词列表
            n (int): n-gram的n值（2=bigram, 3=trigram, 4=fourgram）

        返回:
            List[str]: n-gram字符串列表

        算法：
        使用滑动窗口方法，窗口大小为n，从左到右滑动提取所有可能的n-gram
        """
        if len(words) < n:
            return []
        # 滑动窗口提取n-gram
        return [' '.join(words[i:i+n]) for i in range(len(words) - n + 1)]
    
    def analyze_texts(self, texts: List[str]):
        """分析文本，提取固定搭配"""
        self.texts = texts
        print("正在分析文本中的固定搭配...")
        
        for text in texts:
            words = self.preprocess_text(text)
            
            bigrams = self.extract_ngrams(words, 2)
            trigrams = self.extract_ngrams(words, 3)
            fourgrams = self.extract_ngrams(words, 4)
            
            self.bigrams.update(bigrams)
            self.trigrams.update(trigrams)
            self.fourgrams.update(fourgrams)
        
        all_ngrams = dict(self.bigrams)
        all_ngrams.update(dict(self.trigrams))
        all_ngrams.update(dict(self.fourgrams))
        
        self._populate_data_structures(all_ngrams)
        
        print(f"分析完成！")
        print(f"找到 {len(self.bigrams)} 个二元组合")
        print(f"找到 {len(self.trigrams)} 个三元组合")
        print(f"找到 {len(self.fourgrams)} 个四元组合")
    
    def _populate_data_structures(self, ngrams: Dict[str, int]):
        """填充数据结构"""
        filtered_ngrams = {phrase: freq for phrase, freq in ngrams.items() if freq >= 2}
        
        for phrase, frequency in filtered_ngrams.items():
            self.hash_table[phrase] = frequency
            self.trie.insert(phrase, frequency)
            self.sorted_array.insert(phrase, frequency)
    
    def find_collocations_hash(self, min_frequency: int = 2) -> List[Tuple[str, int]]:
        """使用哈希表查找固定搭配"""
        print(f"\n=== 使用哈希表查找固定搭配（最小频率: {min_frequency}）===")
        
        collocations = [(phrase, freq) for phrase, freq in self.hash_table.items() 
                       if freq >= min_frequency]
        collocations.sort(key=lambda x: x[1], reverse=True)
        
        print(f"找到 {len(collocations)} 个固定搭配")
        return collocations
    
    def find_collocations_trie(self, patterns: List[str]) -> List[Tuple[str, int]]:
        """使用前缀树查找特定模式的固定搭配"""
        print(f"\n=== 使用前缀树查找固定搭配 ===")
        
        results = []
        for pattern in patterns:
            if self.trie.search(pattern):
                frequency = self.trie.get_frequency(pattern)
                results.append((pattern, frequency))
                print(f"找到模式 '{pattern}': 频率 {frequency}")
            else:
                print(f"未找到模式 '{pattern}'")
        
        return results
    
    def get_top_collocations_sorted(self, k: int = 10) -> List[Tuple[int, str]]:
        """使用排序数组获取频率最高的k个固定搭配"""
        print(f"\n=== 使用排序数组获取前 {k} 个高频固定搭配 ===")
        
        top_collocations = self.sorted_array.get_top_k(k)
        top_collocations.reverse()
        
        for i, (freq, phrase) in enumerate(top_collocations, 1):
            print(f"{i}. '{phrase}': {freq} 次")
        
        return top_collocations
    
    def search_collocation(self, phrase: str) -> Dict[str, int]:
        """在所有数据结构中搜索固定搭配"""
        print(f"\n=== 搜索固定搭配: '{phrase}' ===")
        
        results = {
            'hash_table': self.hash_table.get(phrase, 0),
            'trie': self.trie.get_frequency(phrase),
            'sorted_array': self.sorted_array.search(phrase)
        }
        
        for method, frequency in results.items():
            print(f"{method}: {frequency} 次")
        
        return results

# =============================================================================
# 模块3: 基础反向索引 (BasicInvertedIndex)
# =============================================================================

class Document:
    """
    文档类 - 表示索引中的单个文档

    功能说明：
    封装文档的基本信息和预处理后的内容，为反向索引提供标准化的文档表示。

    属性：
    - doc_id: 文档唯一标识符
    - content: 原始文档内容
    - title: 文档标题
    - words: 预处理后的单词列表
    - word_count: 文档中的单词总数
    """
    def __init__(self, doc_id: int, content: str, title: str = ""):
        """
        初始化文档对象

        参数:
            doc_id (int): 文档ID
            content (str): 文档内容
            title (str): 文档标题，可选
        """
        self.doc_id = doc_id
        self.content = content
        self.title = title
        self.words = self._preprocess_content()  # 预处理得到单词列表
        self.word_count = len(self.words)        # 统计单词数量

    def _preprocess_content(self) -> List[str]:
        """
        预处理文档内容

        返回:
            List[str]: 清理后的单词列表

        处理步骤:
        1. 转换为小写
        2. 移除标点符号
        3. 分割成单词并过滤空字符串
        """
        # 移除标点符号，转为小写
        text = re.sub(r'[^\w\s]', ' ', self.content.lower())
        # 分割并过滤空字符串
        words = [word for word in text.split() if word.strip()]
        return words

class PostingList:
    """
    倒排列表类 - 存储单个词项的文档信息

    功能说明：
    倒排列表是反向索引的核心数据结构，存储每个词在哪些文档中出现，
    以及在每个文档中的具体位置信息。

    属性：
    - documents: 字典，键为文档ID，值为该词在文档中的位置列表
    - document_frequency: 包含该词的文档数量（用于计算IDF）

    时间复杂度：
    - 添加文档: O(1)
    - 获取文档集合: O(1)
    - 获取位置信息: O(1)
    """
    def __init__(self):
        """初始化倒排列表"""
        self.documents = {}           # 文档ID -> 位置列表的映射
        self.document_frequency = 0   # 文档频率（包含该词的文档数）

    def add_document(self, doc_id: int, positions: List[int]):
        """
        添加文档和该词在文档中的位置信息

        参数:
            doc_id (int): 文档ID
            positions (List[int]): 该词在文档中的位置列表
        """
        # 如果是新文档，增加文档频率
        if doc_id not in self.documents:
            self.document_frequency += 1
        # 存储位置信息
        self.documents[doc_id] = positions

    def get_documents(self) -> Set[int]:
        """
        获取包含该词的所有文档ID集合

        返回:
            Set[int]: 文档ID集合
        """
        return set(self.documents.keys())

    def get_positions(self, doc_id: int) -> List[int]:
        """
        获取该词在特定文档中的位置列表

        参数:
            doc_id (int): 文档ID

        返回:
            List[int]: 位置列表，如果文档不存在则返回空列表
        """
        return self.documents.get(doc_id, [])

class BasicInvertedIndex:
    """
    基础反向索引类 - 实现文档检索的核心数据结构

    功能说明：
    反向索引(Inverted Index)是信息检索系统的核心数据结构，它建立了从词项到
    包含该词项的文档列表的映射关系。与正向索引（文档->词项）相反，反向索引
    支持高效的关键词搜索。

    数据结构：
    - index: 主索引，词项 -> PostingList的映射
    - documents: 文档存储，文档ID -> Document对象的映射
    - vocabulary: 词汇表，包含所有唯一词项
    - total_documents: 文档总数

    支持的搜索类型：
    1. 单词搜索：查找包含特定词的文档
    2. 多词AND搜索：查找同时包含多个词的文档
    3. 多词OR搜索：查找包含任一词的文档
    4. 短语搜索：查找包含完整短语的文档
    5. 排序搜索：基于TF-IDF分数排序的搜索结果

    时间复杂度：
    - 构建索引: O(N*M)，N为文档数，M为平均文档长度
    - 单词搜索: O(1)
    - 多词搜索: O(k*D)，k为查询词数，D为平均文档列表长度
    """
    def __init__(self):
        """
        初始化基础反向索引

        数据结构说明：
        - index: 核心倒排索引，使用defaultdict自动创建PostingList
        - documents: 文档存储字典
        - total_documents: 文档计数器
        - vocabulary: 词汇表集合，用于快速词汇查找
        """
        self.index = defaultdict(PostingList)  # 主倒排索引
        self.documents = {}                    # 文档存储
        self.total_documents = 0               # 文档总数
        self.vocabulary = set()                # 词汇表

    def add_document(self, doc_id: int, content: str, title: str = ""):
        """
        添加单个文档到索引

        参数:
            doc_id (int): 文档唯一标识符
            content (str): 文档内容
            title (str): 文档标题，可选

        索引构建流程:
        1. 创建Document对象并预处理内容
        2. 统计每个词在文档中的位置
        3. 更新词汇表
        4. 将位置信息添加到对应词项的倒排列表中
        """
        # 创建文档对象
        document = Document(doc_id, content, title)
        self.documents[doc_id] = document
        self.total_documents += 1

        # 统计词位置信息
        word_positions = defaultdict(list)

        # 遍历文档中的每个词，记录位置
        for position, word in enumerate(document.words):
            word_positions[word].append(position)
            self.vocabulary.add(word)

        # 将位置信息添加到倒排索引中
        for word, positions in word_positions.items():
            self.index[word].add_document(doc_id, positions)

    def build_index(self, texts: List[str]):
        """
        批量构建索引

        参数:
            texts (List[str]): 文本列表

        功能:
        遍历所有文本，为每个文本创建文档并添加到索引中
        """
        print("正在构建基础反向索引...")

        # 批量添加文档
        for i, text in enumerate(texts):
            title = f"Document {i+1}"
            self.add_document(i, text, title)

        # 输出索引统计信息
        print(f"索引构建完成！")
        print(f"总文档数: {self.total_documents}")
        print(f"词汇表大小: {len(self.vocabulary)}")

    def search_single_word(self, word: str) -> Set[int]:
        """搜索单个单词"""
        word = word.lower()
        if word in self.index:
            return self.index[word].get_documents()
        return set()

    def search_multiple_words_and(self, words: List[str]) -> Set[int]:
        """多单词AND搜索"""
        if not words:
            return set()

        result = self.search_single_word(words[0])

        for word in words[1:]:
            word_docs = self.search_single_word(word)
            result = result.intersection(word_docs)

            if not result:
                break

        return result

    def search_multiple_words_or(self, words: List[str]) -> Set[int]:
        """多单词OR搜索"""
        result = set()

        for word in words:
            word_docs = self.search_single_word(word)
            result = result.union(word_docs)

        return result

    def phrase_search(self, phrase: str) -> Set[int]:
        """短语搜索"""
        words = phrase.lower().split()
        if len(words) == 1:
            return self.search_single_word(words[0])

        candidate_docs = self.search_multiple_words_and(words)

        result = set()

        for doc_id in candidate_docs:
            if self._contains_phrase(doc_id, words):
                result.add(doc_id)

        return result

    def _contains_phrase(self, doc_id: int, words: List[str]) -> bool:
        """检查文档是否包含完整短语"""
        if not words:
            return False

        first_word_positions = self.index[words[0]].get_positions(doc_id)

        for start_pos in first_word_positions:
            valid_phrase = True
            for i, word in enumerate(words[1:], 1):
                expected_pos = start_pos + i
                word_positions = self.index[word].get_positions(doc_id)

                if expected_pos not in word_positions:
                    valid_phrase = False
                    break

            if valid_phrase:
                return True

        return False

    def calculate_tf_idf(self, word: str, doc_id: int) -> float:
        """计算TF-IDF分数"""
        if word not in self.index or doc_id not in self.documents:
            return 0.0

        word_positions = self.index[word].get_positions(doc_id)
        tf = len(word_positions) / self.documents[doc_id].word_count

        df = self.index[word].document_frequency
        idf = math.log(self.total_documents / df) if df > 0 else 0

        return tf * idf

    def ranked_search(self, query: str, top_k: int = 5) -> List[Tuple[int, float]]:
        """排序搜索"""
        words = query.lower().split()
        doc_scores = defaultdict(float)

        for word in words:
            if word in self.index:
                for doc_id in self.index[word].get_documents():
                    doc_scores[doc_id] += self.calculate_tf_idf(word, doc_id)

        ranked_docs = sorted(doc_scores.items(), key=lambda x: x[1], reverse=True)

        return ranked_docs[:top_k]

# =============================================================================
# 模块4: 词法分析反向索引
# =============================================================================

class LexicalDocument(Document):
    """带词法分析的文档类"""
    def __init__(self, doc_id: int, content: str, title: str = ""):
        try:
            if NLTK_AVAILABLE:
                self.lemmatizer = WordNetLemmatizer()
                self.stop_words = set(stopwords.words('english'))
            else:
                raise Exception("NLTK not available")
        except:
            self.lemmatizer = None
            self.stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}

        super().__init__(doc_id, content, title)
        self.pos_tags = []
        self.lemmas = []
        self._perform_lexical_analysis()

    def _preprocess_content(self) -> List[str]:
        """预处理文档内容"""
        try:
            if NLTK_AVAILABLE:
                tokens = word_tokenize(self.content.lower())
                words = [token for token in tokens if token.isalpha() and len(token) > 1]
            else:
                raise Exception("NLTK not available")
        except:
            text = re.sub(r'[^\w\s]', ' ', self.content.lower())
            words = [word for word in text.split() if word.strip() and len(word) > 1]
        return words

    def _perform_lexical_analysis(self):
        """执行词法分析"""
        try:
            if NLTK_AVAILABLE:
                self.pos_tags = pos_tag(self.words)
                self.lemmas = []
                for word, pos in self.pos_tags:
                    wordnet_pos = self._get_wordnet_pos(pos)
                    if wordnet_pos:
                        lemma = self.lemmatizer.lemmatize(word, wordnet_pos)
                    else:
                        lemma = self.lemmatizer.lemmatize(word)
                    self.lemmas.append(lemma)
            else:
                raise Exception("NLTK not available")
        except:
            self.pos_tags = [(word, 'NN') for word in self.words]
            self.lemmas = [self._simple_lemmatize(word) for word in self.words]

    def _simple_lemmatize(self, word: str) -> str:
        """简化的词形还原"""
        if word.endswith('ies'):
            return word[:-3] + 'y'
        elif word.endswith('s') and not word.endswith('ss'):
            return word[:-1]
        elif word.endswith('ed'):
            return word[:-2]
        elif word.endswith('ing'):
            return word[:-3]
        return word

    def _get_wordnet_pos(self, treebank_tag: str) -> Optional[str]:
        """将TreeBank POS标签转换为WordNet POS标签"""
        if not NLTK_AVAILABLE:
            return None
        if treebank_tag.startswith('J'):
            return wordnet.ADJ
        elif treebank_tag.startswith('V'):
            return wordnet.VERB
        elif treebank_tag.startswith('N'):
            return wordnet.NOUN
        elif treebank_tag.startswith('R'):
            return wordnet.ADV
        else:
            return None

class LexicalPostingList(PostingList):
    """带词法信息的倒排列表"""
    def __init__(self):
        super().__init__()
        self.pos_info = defaultdict(list)
        self.lemma_info = defaultdict(list)

    def add_document_with_lexical_info(self, doc_id: int, positions: List[int],
                                     pos_tags: List[str], lemmas: List[str]):
        """添加带词法信息的文档"""
        self.add_document(doc_id, positions)

        for i, pos in enumerate(positions):
            if i < len(pos_tags):
                self.pos_info[doc_id].append((pos, pos_tags[i]))
            if i < len(lemmas):
                self.lemma_info[doc_id].append((pos, lemmas[i]))

class LexicalInvertedIndex(BasicInvertedIndex):
    """词法分析反向索引"""
    def __init__(self):
        super().__init__()
        self.lemma_index = defaultdict(LexicalPostingList)
        self.pos_index = defaultdict(LexicalPostingList)
        try:
            if NLTK_AVAILABLE:
                self.stop_words = set(stopwords.words('english'))
                self.lemmatizer = WordNetLemmatizer()
            else:
                raise Exception("NLTK not available")
        except:
            self.stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
            self.lemmatizer = None

    def add_document(self, doc_id: int, content: str, title: str = ""):
        """添加文档到词法索引"""
        document = LexicalDocument(doc_id, content, title)
        self.documents[doc_id] = document
        self.total_documents += 1

        word_positions = defaultdict(list)
        lemma_positions = defaultdict(list)
        pos_positions = defaultdict(list)

        for position, (word, pos_tag) in enumerate(zip(document.words, [tag for _, tag in document.pos_tags])):
            word_positions[word].append(position)
            self.vocabulary.add(word)

            if position < len(document.lemmas):
                lemma = document.lemmas[position]
                lemma_positions[lemma].append(position)

            pos_positions[pos_tag].append(position)

        for word, positions in word_positions.items():
            if word not in self.index:
                self.index[word] = LexicalPostingList()

            word_pos_tags = [document.pos_tags[pos][1] for pos in positions if pos < len(document.pos_tags)]
            word_lemmas = [document.lemmas[pos] for pos in positions if pos < len(document.lemmas)]

            self.index[word].add_document_with_lexical_info(doc_id, positions, word_pos_tags, word_lemmas)

        for lemma, positions in lemma_positions.items():
            if lemma not in self.lemma_index:
                self.lemma_index[lemma] = LexicalPostingList()

            lemma_pos_tags = [document.pos_tags[pos][1] for pos in positions if pos < len(document.pos_tags)]
            lemma_lemmas = [lemma] * len(positions)

            self.lemma_index[lemma].add_document_with_lexical_info(doc_id, positions, lemma_pos_tags, lemma_lemmas)

        for pos_tag, positions in pos_positions.items():
            if pos_tag not in self.pos_index:
                self.pos_index[pos_tag] = LexicalPostingList()

            # 获取该词性对应位置的词形信息
            pos_lemmas = [document.lemmas[pos] for pos in positions if pos < len(document.lemmas)]

            self.pos_index[pos_tag].add_document_with_lexical_info(doc_id, positions, [pos_tag] * len(positions), pos_lemmas)

    def search_by_lemma(self, lemma: str) -> Set[int]:
        """按词形还原搜索"""
        lemma = lemma.lower()
        if lemma in self.lemma_index:
            return self.lemma_index[lemma].get_documents()
        return set()

    def search_by_pos(self, pos_tag: str) -> Set[int]:
        """按词性搜索"""
        if pos_tag in self.pos_index:
            return self.pos_index[pos_tag].get_documents()
        return set()

    def search_lemma_multiple_words(self, lemmas: List[str], operation: str = "and") -> Set[int]:
        """多词形还原搜索"""
        if not lemmas:
            return set()

        if operation == "and":
            result = self.search_by_lemma(lemmas[0])
            for lemma in lemmas[1:]:
                result = result.intersection(self.search_by_lemma(lemma))
                if not result:
                    break
        else:
            result = set()
            for lemma in lemmas:
                result = result.union(self.search_by_lemma(lemma))

        return result

# =============================================================================
# 模块5: 词向量反向索引
# =============================================================================

class SimpleWordVectorizer:
    """简化的词向量实现"""
    def __init__(self, vector_size: int = 100):
        self.vector_size = vector_size
        self.word_vectors = {}
        self.vocabulary = set()
        self.tfidf_vectorizer = TfidfVectorizer(max_features=1000)
        self.is_trained = False

    def train(self, sentences: List[List[str]]):
        """训练简化的词向量"""
        print("使用简化方法训练词向量...")

        for sentence in sentences:
            self.vocabulary.update(sentence)

        corpus = [' '.join(sentence) for sentence in sentences]
        # 训练TF-IDF向量化器（用于特征提取）
        self.tfidf_vectorizer.fit_transform(corpus)

        feature_names = self.tfidf_vectorizer.get_feature_names_out()

        for word in self.vocabulary:
            if word in feature_names:
                word_idx = list(feature_names).index(word)
                vector = np.random.normal(0, 0.1, self.vector_size)
                vector[word_idx % self.vector_size] += 1.0
                self.word_vectors[word] = vector / np.linalg.norm(vector)
            else:
                vector = np.random.normal(0, 0.1, self.vector_size)
                self.word_vectors[word] = vector / np.linalg.norm(vector)

        self.is_trained = True
        print(f"训练完成，词汇表大小: {len(self.vocabulary)}")

    def get_vector(self, word: str) -> Optional[np.ndarray]:
        """获取词向量"""
        return self.word_vectors.get(word.lower())

    def similarity(self, word1: str, word2: str) -> float:
        """计算词相似度"""
        vec1 = self.get_vector(word1)
        vec2 = self.get_vector(word2)

        if vec1 is not None and vec2 is not None:
            return float(cosine_similarity([vec1], [vec2])[0][0])
        return 0.0

    def most_similar(self, word: str, topn: int = 10) -> List[Tuple[str, float]]:
        """找到最相似的词"""
        word_vec = self.get_vector(word)
        if word_vec is None:
            return []

        similarities = []
        for other_word, other_vec in self.word_vectors.items():
            if other_word != word.lower():
                sim = float(cosine_similarity([word_vec], [other_vec])[0][0])
                similarities.append((other_word, sim))

        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:topn]

class VectorInvertedIndex(LexicalInvertedIndex):
    """基于词向量的反向索引"""
    def __init__(self, vector_model_type: str = "simple", vector_size: int = 100):
        super().__init__()
        self.vector_model_type = vector_model_type
        self.vector_size = vector_size
        self.word_vectors = None
        self.document_vectors = {}
        self.similarity_threshold = 0.6

        self._initialize_vector_model()

    def _initialize_vector_model(self):
        """初始化词向量模型"""
        print("使用简化词向量模型...")
        self.word_vectors = SimpleWordVectorizer(self.vector_size)

    def build_index(self, texts: List[str]):
        """构建向量索引"""
        print("正在构建词向量反向索引...")

        super().build_index(texts)

        self._train_vector_model(texts)
        self._build_document_vectors()

        print("词向量索引构建完成！")

    def _train_vector_model(self, texts: List[str]):
        """训练词向量模型"""
        sentences = []
        for i, _ in enumerate(texts):
            doc = self.documents[i]
            sentences.append(doc.words)

        self.word_vectors.train(sentences)

    def _build_document_vectors(self):
        """构建文档向量"""
        print("构建文档向量...")

        for doc_id, document in self.documents.items():
            doc_vector = self._get_average_word_vector(document.words)
            if doc_vector is not None:
                self.document_vectors[doc_id] = doc_vector

    def _get_average_word_vector(self, words: List[str]) -> Optional[np.ndarray]:
        """获取平均词向量作为文档向量"""
        vectors = []

        for word in words:
            vector = self.word_vectors.get_vector(word)
            if vector is not None:
                vectors.append(vector)

        if vectors:
            return np.mean(vectors, axis=0)
        return None

    def get_word_vector(self, word: str) -> Optional[np.ndarray]:
        """获取单词向量"""
        return self.word_vectors.get_vector(word)

    def find_similar_words(self, word: str, topn: int = 10) -> List[Tuple[str, float]]:
        """找到相似词"""
        return self.word_vectors.most_similar(word, topn)

    def semantic_search(self, query: str, top_k: int = 5) -> List[Tuple[int, float]]:
        """语义搜索"""
        query_words = query.lower().split()
        query_vector = self._get_average_word_vector(query_words)

        if query_vector is None:
            return []

        similarities = []
        for doc_id, doc_vector in self.document_vectors.items():
            similarity = cosine_similarity([query_vector], [doc_vector])[0][0]
            similarities.append((doc_id, float(similarity)))

        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:top_k]

    def expanded_search(self, query: str, expansion_count: int = 3) -> Set[int]:
        """扩展搜索"""
        query_words = query.lower().split()
        expanded_words = set(query_words)

        for word in query_words:
            similar_words = self.find_similar_words(word, expansion_count)
            for similar_word, similarity in similar_words:
                if similarity > self.similarity_threshold:
                    expanded_words.add(similar_word)

        print(f"原查询词: {query_words}")
        print(f"扩展后词汇: {expanded_words}")

        return self.search_multiple_words_or(list(expanded_words))

    def hybrid_search(self, query: str, weights: Dict[str, float] = None) -> List[Tuple[int, float]]:
        """混合搜索"""
        if weights is None:
            weights = {"keyword": 0.4, "semantic": 0.6}

        keyword_results = self.ranked_search(query, top_k=10)
        keyword_scores = {doc_id: score for doc_id, score in keyword_results}

        semantic_results = self.semantic_search(query, top_k=10)
        semantic_scores = {doc_id: score for doc_id, score in semantic_results}

        all_docs = set(keyword_scores.keys()) | set(semantic_scores.keys())
        hybrid_scores = []

        for doc_id in all_docs:
            keyword_score = keyword_scores.get(doc_id, 0)
            semantic_score = semantic_scores.get(doc_id, 0)

            if keyword_results:
                max_keyword = max(score for _, score in keyword_results)
                keyword_score = keyword_score / max_keyword if max_keyword > 0 else 0

            if semantic_results:
                max_semantic = max(score for _, score in semantic_results)
                semantic_score = semantic_score / max_semantic if max_semantic > 0 else 0

            hybrid_score = (weights["keyword"] * keyword_score +
                          weights["semantic"] * semantic_score)

            hybrid_scores.append((doc_id, hybrid_score))

        hybrid_scores.sort(key=lambda x: x[1], reverse=True)
        return hybrid_scores

# =============================================================================
# 模块6: 正则表达式DFA
# =============================================================================

class NFAState:
    """NFA状态类"""
    def __init__(self, state_id: int, is_final: bool = False):
        self.state_id = state_id
        self.is_final = is_final
        self.transitions = defaultdict(set)
        self.epsilon_transitions = set()

class DFAState:
    """DFA状态类"""
    def __init__(self, state_id: int, nfa_states: Set[int], is_final: bool = False):
        self.state_id = state_id
        self.nfa_states = nfa_states
        self.is_final = is_final
        self.transitions = {}

class RegexDFA:
    """正则表达式DFA类"""
    def __init__(self, regex_pattern: str):
        self.regex_pattern = regex_pattern
        self.nfa_states = {}
        self.dfa_states = {}
        self.start_state = 0
        self.current_state_id = 0
        self.alphabet = set()

        self._build_dfa()

    def _get_next_state_id(self) -> int:
        """获取下一个状态ID"""
        state_id = self.current_state_id
        self.current_state_id += 1
        return state_id

    def _build_nfa_from_regex(self, pattern: str) -> Tuple[int, int]:
        """从正则表达式构建NFA"""
        if not pattern:
            start = self._get_next_state_id()
            end = self._get_next_state_id()
            self.nfa_states[start] = NFAState(start)
            self.nfa_states[end] = NFAState(end, is_final=True)
            self.nfa_states[start].epsilon_transitions.add(end)
            return start, end

        return self._parse_regex(pattern)

    def _parse_regex(self, pattern: str) -> Tuple[int, int]:
        """解析正则表达式"""
        if len(pattern) == 1:
            return self._create_char_nfa(pattern)

        if '|' not in pattern and '*' not in pattern and '+' not in pattern and '?' not in pattern:
            return self._create_concatenation_nfa(pattern)

        if '|' in pattern:
            return self._create_alternation_nfa(pattern)

        if pattern.endswith('*'):
            base_pattern = pattern[:-1]
            base_start, base_end = self._parse_regex(base_pattern)
            return self._create_kleene_star_nfa(base_start, base_end)

        if pattern.endswith('+'):
            base_pattern = pattern[:-1]
            base_start, base_end = self._parse_regex(base_pattern)
            return self._create_plus_nfa(base_start, base_end)

        if pattern.endswith('?'):
            base_pattern = pattern[:-1]
            base_start, base_end = self._parse_regex(base_pattern)
            return self._create_optional_nfa(base_start, base_end)

        return self._create_concatenation_nfa(pattern)

    def _create_char_nfa(self, char: str) -> Tuple[int, int]:
        """创建单字符NFA"""
        start = self._get_next_state_id()
        end = self._get_next_state_id()

        self.nfa_states[start] = NFAState(start)
        self.nfa_states[end] = NFAState(end, is_final=True)

        self.nfa_states[start].transitions[char].add(end)
        self.alphabet.add(char)

        return start, end

    def _create_concatenation_nfa(self, pattern: str) -> Tuple[int, int]:
        """创建连接NFA"""
        if len(pattern) == 1:
            return self._create_char_nfa(pattern)

        first_char = pattern[0]
        rest_pattern = pattern[1:]

        first_start, first_end = self._create_char_nfa(first_char)
        rest_start, rest_end = self._parse_regex(rest_pattern)

        self.nfa_states[first_end].is_final = False
        self.nfa_states[first_end].epsilon_transitions.add(rest_start)

        return first_start, rest_end

    def _create_alternation_nfa(self, pattern: str) -> Tuple[int, int]:
        """创建选择NFA"""
        parts = pattern.split('|', 1)
        left_pattern = parts[0]
        right_pattern = parts[1]

        left_start, left_end = self._parse_regex(left_pattern)
        right_start, right_end = self._parse_regex(right_pattern)

        new_start = self._get_next_state_id()
        new_end = self._get_next_state_id()

        self.nfa_states[new_start] = NFAState(new_start)
        self.nfa_states[new_end] = NFAState(new_end, is_final=True)

        self.nfa_states[new_start].epsilon_transitions.add(left_start)
        self.nfa_states[new_start].epsilon_transitions.add(right_start)

        self.nfa_states[left_end].is_final = False
        self.nfa_states[right_end].is_final = False
        self.nfa_states[left_end].epsilon_transitions.add(new_end)
        self.nfa_states[right_end].epsilon_transitions.add(new_end)

        return new_start, new_end

    def _create_kleene_star_nfa(self, start: int, end: int) -> Tuple[int, int]:
        """创建Kleene星号NFA"""
        new_start = self._get_next_state_id()
        new_end = self._get_next_state_id()

        self.nfa_states[new_start] = NFAState(new_start)
        self.nfa_states[new_end] = NFAState(new_end, is_final=True)

        self.nfa_states[new_start].epsilon_transitions.add(start)
        self.nfa_states[new_start].epsilon_transitions.add(new_end)

        self.nfa_states[end].is_final = False
        self.nfa_states[end].epsilon_transitions.add(start)
        self.nfa_states[end].epsilon_transitions.add(new_end)

        return new_start, new_end

    def _create_plus_nfa(self, start: int, end: int) -> Tuple[int, int]:
        """创建加号NFA"""
        star_start, star_end = self._create_kleene_star_nfa(start, end)

        new_start = self._get_next_state_id()
        self.nfa_states[new_start] = NFAState(new_start)
        self.nfa_states[new_start].epsilon_transitions.add(start)

        self.nfa_states[end].epsilon_transitions.add(star_start)

        return new_start, star_end

    def _create_optional_nfa(self, start: int, end: int) -> Tuple[int, int]:
        """创建可选NFA"""
        new_start = self._get_next_state_id()
        self.nfa_states[new_start] = NFAState(new_start)

        self.nfa_states[new_start].epsilon_transitions.add(start)
        self.nfa_states[new_start].epsilon_transitions.add(end)

        return new_start, end

    def _epsilon_closure(self, states: Set[int]) -> Set[int]:
        """计算epsilon闭包"""
        closure = set(states)
        stack = list(states)

        while stack:
            state = stack.pop()
            if state in self.nfa_states:
                for next_state in self.nfa_states[state].epsilon_transitions:
                    if next_state not in closure:
                        closure.add(next_state)
                        stack.append(next_state)

        return closure

    def _nfa_to_dfa(self, nfa_start: int):
        """将NFA转换为DFA"""
        initial_closure = self._epsilon_closure({nfa_start})
        initial_is_final = any(self.nfa_states[s].is_final for s in initial_closure if s in self.nfa_states)

        dfa_start_id = 0
        self.dfa_states[dfa_start_id] = DFAState(dfa_start_id, initial_closure, initial_is_final)

        state_mapping = {frozenset(initial_closure): dfa_start_id}
        unprocessed = [initial_closure]
        next_dfa_id = 1

        while unprocessed:
            current_nfa_states = unprocessed.pop(0)
            current_dfa_id = state_mapping[frozenset(current_nfa_states)]

            for symbol in self.alphabet:
                next_nfa_states = set()

                for nfa_state in current_nfa_states:
                    if nfa_state in self.nfa_states:
                        next_nfa_states.update(self.nfa_states[nfa_state].transitions[symbol])

                if next_nfa_states:
                    next_closure = self._epsilon_closure(next_nfa_states)
                    next_closure_frozen = frozenset(next_closure)

                    if next_closure_frozen not in state_mapping:
                        is_final = any(self.nfa_states[s].is_final for s in next_closure if s in self.nfa_states)
                        state_mapping[next_closure_frozen] = next_dfa_id
                        self.dfa_states[next_dfa_id] = DFAState(next_dfa_id, next_closure, is_final)
                        unprocessed.append(next_closure)
                        next_dfa_id += 1

                    target_dfa_id = state_mapping[next_closure_frozen]
                    self.dfa_states[current_dfa_id].transitions[symbol] = target_dfa_id

    def _build_dfa(self):
        """构建DFA"""
        print(f"正在为正则表达式 '{self.regex_pattern}' 构建DFA...")

        processed_pattern = self._preprocess_regex(self.regex_pattern)

        nfa_start, _ = self._build_nfa_from_regex(processed_pattern)

        self._nfa_to_dfa(nfa_start)

        print(f"DFA构建完成！")
        print(f"NFA状态数: {len(self.nfa_states)}")
        print(f"DFA状态数: {len(self.dfa_states)}")
        print(f"字母表: {self.alphabet}")

    def _preprocess_regex(self, pattern: str) -> str:
        """预处理正则表达式"""
        processed = pattern.replace('\\d', '[0-9]').replace('\\w', '[a-zA-Z0-9_]')
        return processed

    def match(self, input_string: str) -> bool:
        """检查字符串是否匹配正则表达式"""
        current_state = 0

        for char in input_string:
            if char not in self.alphabet:
                return False

            if current_state in self.dfa_states:
                if char in self.dfa_states[current_state].transitions:
                    current_state = self.dfa_states[current_state].transitions[char]
                else:
                    return False
            else:
                return False

        return (current_state in self.dfa_states and
                self.dfa_states[current_state].is_final)

    def get_dfa_info(self) -> Dict:
        """获取DFA信息"""
        transitions_info = {}
        for state_id, state in self.dfa_states.items():
            transitions_info[state_id] = {
                'is_final': state.is_final,
                'transitions': dict(state.transitions),
                'nfa_states': list(state.nfa_states)
            }

        return {
            'regex_pattern': self.regex_pattern,
            'alphabet': list(self.alphabet),
            'states_count': len(self.dfa_states),
            'start_state': 0,
            'states': transitions_info
        }

# =============================================================================
# 模块7: 简单编程语言解释器
# =============================================================================

class TokenType(Enum):
    """词法单元类型"""
    NUMBER = "NUMBER"
    STRING = "STRING"
    IDENTIFIER = "IDENTIFIER"
    KEYWORD = "KEYWORD"
    OPERATOR = "OPERATOR"
    DELIMITER = "DELIMITER"
    EOF = "EOF"

class Token:
    """词法单元"""
    def __init__(self, token_type: TokenType, value: str, line: int = 0):
        self.type = token_type
        self.value = value
        self.line = line

    def __repr__(self):
        return f"Token({self.type}, {self.value})"

class Lexer:
    """词法分析器"""
    def __init__(self, text: str):
        self.text = text
        self.pos = 0
        self.current_char = self.text[self.pos] if self.text else None
        self.line = 1

        self.keywords = {
            'print', 'read', 'if', 'else', 'while', 'for', 'let', 'def', 'return',
            'true', 'false', 'and', 'or', 'not', 'in', 'end'
        }

        self.operators = {
            '+', '-', '*', '/', '%', '=', '==', '!=', '<', '>', '<=', '>=',
            '&&', '||', '!', '+=', '-=', '*=', '/='
        }

        self.delimiters = {'(', ')', '[', ']', '{', '}', ',', ';', ':', '\n'}

    def advance(self):
        """移动到下一个字符"""
        if self.current_char == '\n':
            self.line += 1

        self.pos += 1
        if self.pos >= len(self.text):
            self.current_char = None
        else:
            self.current_char = self.text[self.pos]

    def skip_whitespace(self):
        """跳过空白字符"""
        while self.current_char and self.current_char in ' \t\r':
            self.advance()

    def skip_comment(self):
        """跳过注释"""
        if self.current_char == '#':
            while self.current_char and self.current_char != '\n':
                self.advance()

    def read_number(self) -> Token:
        """读取数字"""
        result = ''
        while self.current_char and (self.current_char.isdigit() or self.current_char == '.'):
            result += self.current_char
            self.advance()

        return Token(TokenType.NUMBER, result, self.line)

    def read_string(self) -> Token:
        """读取字符串"""
        quote_char = self.current_char
        self.advance()

        result = ''
        while self.current_char and self.current_char != quote_char:
            if self.current_char == '\\':
                self.advance()
                if self.current_char == 'n':
                    result += '\n'
                elif self.current_char == 't':
                    result += '\t'
                elif self.current_char == '\\':
                    result += '\\'
                elif self.current_char == quote_char:
                    result += quote_char
                else:
                    result += self.current_char
            else:
                result += self.current_char
            self.advance()

        if self.current_char == quote_char:
            self.advance()

        return Token(TokenType.STRING, result, self.line)

    def read_identifier(self) -> Token:
        """读取标识符或关键字"""
        result = ''
        while self.current_char and (self.current_char.isalnum() or self.current_char == '_'):
            result += self.current_char
            self.advance()

        token_type = TokenType.KEYWORD if result in self.keywords else TokenType.IDENTIFIER
        return Token(token_type, result, self.line)

    def read_operator(self) -> Token:
        """读取操作符"""
        result = self.current_char
        self.advance()

        if self.current_char and result + self.current_char in self.operators:
            result += self.current_char
            self.advance()

        return Token(TokenType.OPERATOR, result, self.line)

    def get_next_token(self) -> Token:
        """获取下一个词法单元"""
        while self.current_char:
            if self.current_char in ' \t\r':
                self.skip_whitespace()
                continue

            if self.current_char == '#':
                self.skip_comment()
                continue

            if self.current_char.isdigit():
                return self.read_number()

            if self.current_char in '"\'':
                return self.read_string()

            if self.current_char.isalpha() or self.current_char == '_':
                return self.read_identifier()

            if self.current_char in self.delimiters:
                char = self.current_char
                self.advance()
                return Token(TokenType.DELIMITER, char, self.line)

            if self.current_char in '+-*/%=<>!&|':
                return self.read_operator()

            char = self.current_char
            self.advance()
            raise SyntaxError(f"未知字符 '{char}' 在第 {self.line} 行")

        return Token(TokenType.EOF, '', self.line)

class SimpleInterpreter:
    """简单编程语言解释器"""
    def __init__(self):
        self.variables = {}
        self.functions = {}
        self.lexer = None
        self.current_token = None
        self.output_buffer = []

    def error(self, message: str):
        """抛出语法错误"""
        line = self.current_token.line if self.current_token else 0
        raise SyntaxError(f"第 {line} 行: {message}")

    def eat(self, token_type: TokenType):
        """消费指定类型的词法单元"""
        if self.current_token.type == token_type:
            self.current_token = self.lexer.get_next_token()
        else:
            self.error(f"期望 {token_type}, 但得到 {self.current_token.type}")

    def parse_expression(self) -> Any:
        """解析表达式"""
        return self.parse_or_expression()

    def parse_or_expression(self) -> Any:
        """解析或表达式"""
        result = self.parse_and_expression()

        while self.current_token.type == TokenType.OPERATOR and self.current_token.value == 'or':
            self.eat(TokenType.OPERATOR)
            right = self.parse_and_expression()
            result = result or right

        return result

    def parse_and_expression(self) -> Any:
        """解析与表达式"""
        result = self.parse_equality_expression()

        while self.current_token.type == TokenType.OPERATOR and self.current_token.value == 'and':
            self.eat(TokenType.OPERATOR)
            right = self.parse_equality_expression()
            result = result and right

        return result

    def parse_equality_expression(self) -> Any:
        """解析相等性表达式"""
        result = self.parse_relational_expression()

        while (self.current_token.type == TokenType.OPERATOR and
               self.current_token.value in ['==', '!=']):
            op = self.current_token.value
            self.eat(TokenType.OPERATOR)
            right = self.parse_relational_expression()

            if op == '==':
                result = result == right
            elif op == '!=':
                result = result != right

        return result

    def parse_relational_expression(self) -> Any:
        """解析关系表达式"""
        result = self.parse_additive_expression()

        while (self.current_token.type == TokenType.OPERATOR and
               self.current_token.value in ['<', '>', '<=', '>=']):
            op = self.current_token.value
            self.eat(TokenType.OPERATOR)
            right = self.parse_additive_expression()

            if op == '<':
                result = result < right
            elif op == '>':
                result = result > right
            elif op == '<=':
                result = result <= right
            elif op == '>=':
                result = result >= right

        return result

    def parse_additive_expression(self) -> Any:
        """解析加减表达式"""
        result = self.parse_multiplicative_expression()

        while (self.current_token.type == TokenType.OPERATOR and
               self.current_token.value in ['+', '-']):
            op = self.current_token.value
            self.eat(TokenType.OPERATOR)
            right = self.parse_multiplicative_expression()

            if op == '+':
                result = result + right
            elif op == '-':
                result = result - right

        return result

    def parse_multiplicative_expression(self) -> Any:
        """解析乘除表达式"""
        result = self.parse_unary_expression()

        while (self.current_token.type == TokenType.OPERATOR and
               self.current_token.value in ['*', '/', '%']):
            op = self.current_token.value
            self.eat(TokenType.OPERATOR)
            right = self.parse_unary_expression()

            if op == '*':
                result = result * right
            elif op == '/':
                if right == 0:
                    self.error("除零错误")
                result = result / right
            elif op == '%':
                result = result % right

        return result

    def parse_unary_expression(self) -> Any:
        """解析一元表达式"""
        if self.current_token.type == TokenType.OPERATOR and self.current_token.value in ['+', '-', 'not']:
            op = self.current_token.value
            self.eat(TokenType.OPERATOR)
            expr = self.parse_unary_expression()

            if op == '+':
                return +expr
            elif op == '-':
                return -expr
            elif op == 'not':
                return not expr

        return self.parse_primary_expression()

    def parse_primary_expression(self) -> Any:
        """解析基本表达式"""
        if self.current_token.type == TokenType.NUMBER:
            value = self.current_token.value
            self.eat(TokenType.NUMBER)
            return float(value) if '.' in value else int(value)

        elif self.current_token.type == TokenType.STRING:
            value = self.current_token.value
            self.eat(TokenType.STRING)
            return value

        elif self.current_token.type == TokenType.KEYWORD and self.current_token.value in ['true', 'false']:
            value = self.current_token.value == 'true'
            self.eat(TokenType.KEYWORD)
            return value

        elif self.current_token.type == TokenType.IDENTIFIER:
            var_name = self.current_token.value
            self.eat(TokenType.IDENTIFIER)

            if var_name not in self.variables:
                self.error(f"未定义的变量 '{var_name}'")

            return self.variables[var_name]

        elif self.current_token.type == TokenType.DELIMITER and self.current_token.value == '(':
            self.eat(TokenType.DELIMITER)
            result = self.parse_expression()
            self.eat(TokenType.DELIMITER)
            return result

        else:
            self.error(f"意外的词法单元 {self.current_token}")

    def execute_statement(self):
        """执行语句"""
        if self.current_token.type == TokenType.KEYWORD:
            if self.current_token.value == 'let':
                self.execute_assignment()
            elif self.current_token.value == 'print':
                self.execute_print()
            elif self.current_token.value == 'read':
                self.execute_read()
            elif self.current_token.value == 'if':
                self.execute_if()
            elif self.current_token.value == 'while':
                self.execute_while()
            else:
                self.error(f"未知的关键字 '{self.current_token.value}'")

        elif self.current_token.type == TokenType.IDENTIFIER:
            var_name = self.current_token.value
            self.eat(TokenType.IDENTIFIER)

            if self.current_token.type == TokenType.OPERATOR and self.current_token.value == '=':
                self.eat(TokenType.OPERATOR)
                value = self.parse_expression()
                self.variables[var_name] = value
            else:
                self.error("期望赋值操作符 '='")

    def execute_assignment(self):
        """执行变量赋值"""
        self.eat(TokenType.KEYWORD)

        if self.current_token.type != TokenType.IDENTIFIER:
            self.error("期望变量名")

        var_name = self.current_token.value
        self.eat(TokenType.IDENTIFIER)
        self.eat(TokenType.OPERATOR)

        value = self.parse_expression()
        self.variables[var_name] = value

    def execute_print(self):
        """执行打印语句"""
        self.eat(TokenType.KEYWORD)

        if self.current_token.type == TokenType.DELIMITER and self.current_token.value == '(':
            self.eat(TokenType.DELIMITER)
            value = self.parse_expression()
            self.eat(TokenType.DELIMITER)
        else:
            value = self.parse_expression()

        output = str(value)
        print(output)
        self.output_buffer.append(output)

    def execute_read(self):
        """执行读取语句"""
        self.eat(TokenType.KEYWORD)

        if self.current_token.type != TokenType.IDENTIFIER:
            self.error("期望变量名")

        var_name = self.current_token.value
        self.eat(TokenType.IDENTIFIER)

        try:
            user_input = input(f"请输入 {var_name} 的值: ")
            try:
                value = float(user_input) if '.' in user_input else int(user_input)
            except ValueError:
                value = user_input

            self.variables[var_name] = value
        except EOFError:
            self.variables[var_name] = ""

    def execute_if(self):
        """执行条件语句"""
        self.eat(TokenType.KEYWORD)

        condition = self.parse_expression()

        while (self.current_token.type == TokenType.DELIMITER and
               self.current_token.value == '\n'):
            self.eat(TokenType.DELIMITER)

        if condition:
            while (self.current_token.type != TokenType.KEYWORD or
                   self.current_token.value not in ['else', 'end']):
                if self.current_token.type == TokenType.EOF:
                    self.error("期望 'end' 或 'else'")
                self.execute_statement()
                self.skip_newlines()
        else:
            self.skip_block(['else', 'end'])

        if (self.current_token.type == TokenType.KEYWORD and
            self.current_token.value == 'else'):
            self.eat(TokenType.KEYWORD)
            self.skip_newlines()

            if not condition:
                while (self.current_token.type != TokenType.KEYWORD or
                       self.current_token.value != 'end'):
                    if self.current_token.type == TokenType.EOF:
                        self.error("期望 'end'")
                    self.execute_statement()
                    self.skip_newlines()
            else:
                self.skip_block(['end'])

        if (self.current_token.type == TokenType.KEYWORD and
            self.current_token.value == 'end'):
            self.eat(TokenType.KEYWORD)

    def execute_while(self):
        """执行循环语句"""
        self.eat(TokenType.KEYWORD)

        loop_start_pos = self.lexer.pos
        loop_start_char = self.lexer.current_char
        loop_start_line = self.lexer.line

        while True:
            self.lexer.pos = loop_start_pos
            self.lexer.current_char = loop_start_char
            self.lexer.line = loop_start_line
            self.current_token = self.lexer.get_next_token()

            condition = self.parse_expression()

            if not condition:
                break

            self.skip_newlines()

            while (self.current_token.type != TokenType.KEYWORD or
                   self.current_token.value != 'end'):
                if self.current_token.type == TokenType.EOF:
                    self.error("期望 'end'")
                self.execute_statement()
                self.skip_newlines()

        self.skip_block(['end'])

        if (self.current_token.type == TokenType.KEYWORD and
            self.current_token.value == 'end'):
            self.eat(TokenType.KEYWORD)

    def skip_newlines(self):
        """跳过换行符"""
        while (self.current_token.type == TokenType.DELIMITER and
               self.current_token.value == '\n'):
            self.eat(TokenType.DELIMITER)

    def skip_block(self, end_keywords: List[str]):
        """跳过代码块"""
        depth = 1
        while depth > 0 and self.current_token.type != TokenType.EOF:
            if self.current_token.type == TokenType.KEYWORD:
                if self.current_token.value in ['if', 'while']:
                    depth += 1
                elif self.current_token.value in end_keywords:
                    depth -= 1
            self.current_token = self.lexer.get_next_token()

    def interpret(self, text: str) -> List[str]:
        """解释执行代码"""
        self.lexer = Lexer(text)
        self.current_token = self.lexer.get_next_token()
        self.output_buffer = []

        try:
            while self.current_token.type != TokenType.EOF:
                if (self.current_token.type == TokenType.DELIMITER and
                    self.current_token.value == '\n'):
                    self.eat(TokenType.DELIMITER)
                    continue

                self.execute_statement()
                self.skip_newlines()

            return self.output_buffer

        except Exception as e:
            print(f"运行时错误: {e}")
            return self.output_buffer

# =============================================================================
# 主函数和演示程序
# =============================================================================

def demo_all_modules():
    """
    演示所有7个模块的功能

    这个函数按顺序演示每个模块的核心功能，展示：
    1. 文本收集和预处理
    2. 固定搭配的多种数据结构实现
    3. 基础反向索引的搜索功能
    4. 词法分析增强的搜索
    5. 基于词向量的语义搜索
    6. 正则表达式到DFA的转换
    7. 编程语言解释器的执行

    每个模块都会输出详细的执行结果和统计信息，
    帮助用户理解各种算法的工作原理和效果。
    """
    print("=" * 60)
    print("🚀 文本处理和语言处理系统 - 完整演示")
    print("=" * 60)

    # =========================================================================
    # 模块1: 文本收集器演示
    # =========================================================================
    print("\n📰 1. 文本收集模块")
    print("-" * 30)
    collector = TextCollector()
    # 使用示例文本确保演示的稳定性
    texts = collector.collect_texts(source="sample")
    print(f"✅ 成功收集 {len(texts)} 篇文本，准备进行后续分析")

    # =========================================================================
    # 模块2: 固定搭配查找器演示
    # =========================================================================
    print("\n🔍 2. 固定搭配查找模块")
    print("-" * 30)
    finder = CollocationFinder()
    finder.analyze_texts(texts)

    # 演示哈希表查找（最快的查找方式）
    hash_collocations = finder.find_collocations_hash(min_frequency=2)
    print(f"📊 使用哈希表找到的高频固定搭配（前5个）:")
    for phrase, freq in hash_collocations[:5]:
        print(f"  📌 '{phrase}': {freq} 次")

    # 演示前缀树模式匹配
    patterns = ["climate change", "artificial intelligence"]
    trie_results = finder.find_collocations_trie(patterns)
    if trie_results:
        print(f"🌳 前缀树模式匹配结果:")
        for phrase, freq in trie_results:
            print(f"  🎯 '{phrase}': {freq} 次")

    # =========================================================================
    # 模块3: 基础反向索引演示
    # =========================================================================
    print("\n📚 3. 基础反向索引模块")
    print("-" * 30)
    basic_index = BasicInvertedIndex()
    basic_index.build_index(texts)

    # 演示单词搜索
    docs = basic_index.search_single_word("technology")
    print(f"🔍 单词搜索 'technology': 找到文档 {docs}")

    # 演示短语搜索（位置验证）
    docs = basic_index.phrase_search("artificial intelligence")
    print(f"📝 短语搜索 'artificial intelligence': 找到文档 {docs}")

    # 演示TF-IDF排序搜索
    ranked_results = basic_index.ranked_search("economic development", top_k=3)
    if ranked_results:
        print(f"📈 TF-IDF排序搜索 'economic development':")
        for doc_id, score in ranked_results:
            print(f"  📄 文档 {doc_id}: TF-IDF分数 {score:.4f}")

    # =========================================================================
    # 模块4: 词法分析反向索引演示
    # =========================================================================
    print("\n🔤 4. 词法分析反向索引模块")
    print("-" * 30)
    lexical_index = LexicalInvertedIndex()
    lexical_index.build_index(texts)

    # 演示词形还原搜索
    docs = lexical_index.search_by_lemma("technology")
    print(f"🔄 词形还原搜索 'technology': 找到文档 {docs}")

    # 演示词性搜索
    noun_docs = lexical_index.search_by_pos("NN")
    print(f"📝 词性搜索（名词NN）: 找到 {len(noun_docs)} 个文档")

    # =========================================================================
    # 模块5: 词向量反向索引演示
    # =========================================================================
    print("\n🧠 5. 词向量反向索引模块")
    print("-" * 30)
    # 使用较小的向量维度以加快演示速度
    vector_index = VectorInvertedIndex(vector_model_type="simple", vector_size=50)
    vector_index.build_index(texts)

    # 演示语义搜索
    results = vector_index.semantic_search("economic development", top_k=3)
    print("🎯 语义搜索 'economic development':")
    for doc_id, score in results:
        print(f"  🧠 文档 {doc_id}: 语义相似度 {score:.4f}")

    # 演示相似词查找
    similar_words = vector_index.find_similar_words("technology", topn=3)
    if similar_words:
        print(f"🔗 与 'technology' 相似的词:")
        for word, similarity in similar_words:
            print(f"  💡 '{word}': 相似度 {similarity:.4f}")

    # =========================================================================
    # 模块6: 正则表达式DFA演示
    # =========================================================================
    print("\n🤖 6. 正则表达式DFA模块")
    print("-" * 30)
    # 创建一个简单但有代表性的正则表达式
    dfa = RegexDFA("a*b+")

    test_strings = ["b", "ab", "aab", "aaabbb", "abc"]
    print("🔍 测试正则表达式 'a*b+' (零个或多个a，后跟一个或多个b):")
    for test_str in test_strings:
        result = dfa.match(test_str)
        status = "✅ 匹配" if result else "❌ 不匹配"
        print(f"  🧪 '{test_str}': {status}")

    # 显示DFA信息
    dfa_info = dfa.get_dfa_info()
    print(f"📊 DFA统计: {dfa_info['states_count']} 个状态, 字母表 {dfa_info['alphabet']}")

    # =========================================================================
    # 模块7: 简单编程语言解释器演示
    # =========================================================================
    print("\n💻 7. 简单编程语言解释器模块")
    print("-" * 30)
    interpreter = SimpleInterpreter()

    # 演示程序：包含变量、运算、条件判断
    program = """
    # 计算示例程序
    let x = 10
    let y = 5
    let result = x * y + 2
    print "计算结果:"
    print result

    # 条件判断示例
    if result > 50
        print "结果大于50"
    else
        print "结果小于等于50"
    end
    """

    print("📝 执行程序:")
    print(program.strip())
    print("\n💻 程序输出:")
    try:
        output = interpreter.interpret(program)
        for line in output:
            print(f"  📤 {line}")
    except Exception as e:
        print(f"  ❌ 执行错误: {e}")

    # =========================================================================
    # 演示总结
    # =========================================================================
    print("\n" + "=" * 60)
    print("🎉 所有模块演示完成！")
    print("=" * 60)
    print("📋 演示总结:")
    print("  📰 文本收集: 智能抓取和预处理")
    print("  🔍 固定搭配: 哈希表、前缀树、排序数组")
    print("  📚 基础索引: 倒排索引和TF-IDF搜索")
    print("  🔤 词法分析: 词性标注和词形还原")
    print("  🧠 词向量: 语义搜索和相似度计算")
    print("  🤖 正则DFA: 自动机理论的实际应用")
    print("  💻 解释器: 完整的编程语言实现")
    print("=" * 60)

if __name__ == "__main__":
    """
    程序入口点

    当直接运行此文件时，会自动执行所有模块的演示。
    这展示了整个文本处理系统的完整功能，包括：

    1. 各种数据结构和算法的实现
    2. 从基础到高级的文本处理技术
    3. 实际应用场景的演示
    4. 性能和效果的对比

    用户可以通过观察输出结果来理解每个模块的工作原理，
    也可以修改参数来测试不同的配置和输入。
    """
    demo_all_modules()
