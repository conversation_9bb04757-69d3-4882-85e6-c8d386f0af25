# 文本处理系统使用说明

## 🚀 快速开始

### 1. 运行完整演示
```bash
python text_processing_system.py
```

### 2. 模块化使用
```python
from text_processing_system import TextCollector, CollocationFinder, BasicInvertedIndex

# 收集文本
collector = TextCollector()
texts = collector.collect_texts(source="sample")

# 分析固定搭配
finder = CollocationFinder()
finder.analyze_texts(texts)
top_collocations = finder.get_top_collocations_sorted(10)

# 构建搜索索引
index = BasicInvertedIndex()
index.build_index(texts)
results = index.ranked_search("人工智能", top_k=5)
```

## 📚 核心模块

### 1. 📰 TextCollector - 文本收集器
- **功能**: 从网站收集文本或使用示例文本
- **用法**: `collector.collect_texts(source="sample")`

### 2. 🔍 CollocationFinder - 固定搭配查找器  
- **功能**: 使用哈希表、前缀树、排序数组查找固定搭配
- **用法**: `finder.analyze_texts(texts)`

### 3. 📚 BasicInvertedIndex - 基础反向索引
- **功能**: 构建倒排索引，支持关键词搜索
- **用法**: `index.ranked_search("查询词", top_k=5)`

### 4. 🔤 LexicalInvertedIndex - 词法分析索引
- **功能**: 支持词性标注和词形还原的高级搜索
- **用法**: `lexical_index.search_by_lemma("词根")`

### 5. 🧠 VectorInvertedIndex - 词向量索引
- **功能**: 基于语义相似度的智能搜索
- **用法**: `vector_index.semantic_search("查询", top_k=5)`

### 6. 🤖 RegexDFA - 正则表达式DFA
- **功能**: 将正则表达式转换为确定有限自动机
- **用法**: `dfa = RegexDFA("a*b+"); dfa.match("aabb")`

### 7. 💻 SimpleInterpreter - 编程语言解释器
- **功能**: 解释执行简单编程语言
- **用法**: `interpreter.interpret("let x = 10\nprint x")`

## 🛠️ 技术特点

- **单文件部署**: 所有功能集成在一个文件中
- **容错机制**: 依赖库不可用时自动降级
- **详细注释**: 每个算法都有完整的中文注释
- **模块化设计**: 每个功能独立，易于扩展

## 📋 依赖要求

必需依赖:
- Python 3.8+
- numpy
- scikit-learn
- requests

可选依赖:
- nltk (词法分析，有简化实现)
- beautifulsoup4 (网页抓取，有示例文本)

## 🎯 使用场景

- **学习研究**: 理解文本处理和信息检索算法
- **原型开发**: 快速构建文本处理应用
- **教学演示**: 展示各种数据结构和算法的实现
- **基础框架**: 作为更复杂系统的起点

## 📞 问题反馈

如果遇到问题，请检查：
1. Python版本是否为3.8+
2. 依赖包是否正确安装
3. 网络连接是否正常（如使用网络文本收集）

---
**开始探索**: `python text_processing_system.py` 🎯
