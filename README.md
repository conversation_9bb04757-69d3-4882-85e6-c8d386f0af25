# 文本处理和语言处理系统 - 合并版本

这是一个综合性的文本处理和语言处理系统，所有7个核心模块已合并到单个文件中，便于使用和部署。

## 项目概述

本项目在单个文件中实现了以下7个主要模块：

1. **文本收集器** - 从英文新闻网站收集文本数据
2. **固定搭配查找器** - 使用哈希表、前缀树、排序数组查找文本中的固定搭配
3. **基础反向索引** - 构建反向索引并实现多单词搜索功能
4. **词法分析反向索引** - 考虑词法分析的反向索引，支持词性和词形还原搜索
5. **词向量反向索引** - 基于Word2Vec/BERT的语义搜索反向索引
6. **正则表达式DFA** - 将正则表达式转换为确定有限自动机并进行字符串匹配
7. **简单编程语言解释器** - 支持基本编程功能的解释器

## 文件结构

```
├── text_processing_system.py   # 合并的主文件，包含所有7个模块
├── requirements.txt             # 依赖包列表
└── README.md                    # 项目说明文档
```

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 下载NLTK数据（首次运行时自动下载）

程序会自动下载必要的NLTK数据包，包括：
- punkt（分词器）
- averaged_perceptron_tagger（词性标注器）
- wordnet（词形还原）
- stopwords（停用词）

### 3. 运行程序

```bash
python main.py
```

## 模块详细说明

### 1. 文本收集器 (text_collector.py)

**功能**：从英文新闻网站收集300-400个单词的文本

**特点**：
- 支持从BBC News等网站抓取文本
- 提供示例文本（当网络不可用时）
- 自动文本清理和预处理
- 文本长度控制

**使用示例**：
```python
from text_collector import TextCollector

collector = TextCollector()
texts = collector.collect_texts(source="sample")  # 使用示例文本
# texts = collector.collect_texts(source="bbc")   # 从BBC收集（需要网络）
```

### 2. 固定搭配查找器 (collocation_finder.py)

**功能**：使用多种数据结构查找文本中的固定搭配

**实现的数据结构**：
- **哈希表**：快速存储和查找n-gram频率
- **前缀树(Trie)**：高效的模式匹配
- **排序数组**：按频率排序的固定搭配

**特点**：
- 支持2-gram、3-gram、4-gram提取
- 多种搜索方式对比
- 频率统计和排序

**使用示例**：
```python
from collocation_finder import CollocationFinder

finder = CollocationFinder()
finder.analyze_texts(texts)

# 使用哈希表查找
collocations = finder.find_collocations_hash(min_frequency=2)

# 使用前缀树查找特定模式
patterns = ["artificial intelligence", "climate change"]
results = finder.find_collocations_trie(patterns)

# 获取高频搭配
top_collocations = finder.get_top_collocations_sorted(10)
```

### 3. 基础反向索引 (basic_inverted_index.py)

**功能**：构建反向索引并实现多单词搜索

**特点**：
- 单词到文档的映射
- 支持AND、OR、短语搜索
- TF-IDF分数计算
- 排序搜索结果
- 搜索结果片段提取

**使用示例**：
```python
from basic_inverted_index import BasicInvertedIndex

index = BasicInvertedIndex()
index.build_index(texts)

# 单词搜索
docs = index.search_single_word("technology")

# AND搜索
docs = index.search_multiple_words_and(["artificial", "intelligence"])

# 短语搜索
docs = index.phrase_search("climate change")

# 排序搜索
ranked_results = index.ranked_search("artificial intelligence", top_k=5)
```

### 4. 词法分析反向索引 (lexical_inverted_index.py)

**功能**：考虑词法分析的反向索引，支持高级搜索

**特点**：
- 词性标注(POS tagging)
- 词形还原(Lemmatization)
- 按词性搜索
- 按词形还原搜索
- 高级组合搜索

**使用示例**：
```python
from lexical_inverted_index import LexicalInvertedIndex

lexical_index = LexicalInvertedIndex()
lexical_index.build_index(texts)

# 词形还原搜索
docs = lexical_index.search_by_lemma("technology")

# 词性搜索
docs = lexical_index.search_by_pos("NN")  # 名词

# 高级搜索
query = {
    'words': ['economic'],
    'lemmas': ['grow', 'develop'],
    'pos_tags': ['NN', 'JJ'],
    'operation': 'or'
}
results = lexical_index.advanced_search(query)
```

### 5. 词向量反向索引 (vector_inverted_index.py)

**功能**：基于词向量的语义搜索

**支持的词向量模型**：
- 简化词向量模型（默认，无需额外依赖）
- Word2Vec（需要gensim）
- BERT（需要transformers和torch）

**特点**：
- 语义相似性搜索
- 相似词查找
- 查询扩展搜索
- 混合搜索（关键词+语义）

**使用示例**：
```python
from vector_inverted_index import VectorInvertedIndex

vector_index = VectorInvertedIndex(vector_model_type="simple")
vector_index.build_index(texts)

# 语义搜索
results = vector_index.semantic_search("economic development", top_k=5)

# 相似词查找
similar_words = vector_index.find_similar_words("technology", topn=5)

# 混合搜索
hybrid_results = vector_index.hybrid_search("climate change")
```

### 6. 正则表达式DFA (regex_dfa.py)

**功能**：将正则表达式转换为确定有限自动机

**支持的正则表达式操作**：
- 基本字符匹配
- 连接(concatenation)
- 选择(alternation) `|`
- Kleene星号 `*`
- 加号 `+`
- 问号 `?`

**特点**：
- NFA到DFA的转换
- 字符串匹配验证
- DFA可视化
- 状态转换信息

**使用示例**：
```python
from regex_dfa import RegexDFA

# 创建DFA
dfa = RegexDFA("a*b+")

# 测试字符串匹配
result = dfa.match("aaabbb")  # True
result = dfa.match("abc")     # False

# 获取DFA信息
dfa_info = dfa.get_dfa_info()
print(dfa.visualize_dfa())
```

### 7. 简单编程语言解释器 (simple_interpreter.py)

**功能**：解释执行简单编程语言

**支持的语法**：
- 变量声明和赋值：`let x = 10`
- 算术运算：`+`, `-`, `*`, `/`, `%`
- 比较运算：`==`, `!=`, `<`, `>`, `<=`, `>=`
- 逻辑运算：`and`, `or`, `not`
- 条件语句：`if...else...end`
- 循环语句：`while...end`
- 输入输出：`print`, `read`

**使用示例**：
```python
from simple_interpreter import SimpleInterpreter

interpreter = SimpleInterpreter()

program = """
let x = 10
let y = 20
if x < y
    print "x is smaller"
else
    print "x is larger or equal"
end
"""

output = interpreter.interpret(program)
```

## 运行示例

### 完整演示

运行 `python main.py` 并选择"运行完整演示"，系统会依次演示所有模块的功能。

### 单独测试模块

运行 `python main.py` 并选择"单独测试模块"，可以选择特定模块进行测试。

### 直接运行模块

每个模块都可以独立运行：

```bash
python text_collector.py
python collocation_finder.py
python basic_inverted_index.py
python lexical_inverted_index.py
python vector_inverted_index.py
python regex_dfa.py
python simple_interpreter.py
```

## 技术特点

1. **模块化设计**：每个功能独立实现，便于维护和扩展
2. **多种算法实现**：同一功能使用不同数据结构和算法实现
3. **渐进式复杂度**：从基础到高级，逐步增加功能复杂度
4. **实用性强**：所有功能都有实际应用价值
5. **可扩展性**：易于添加新功能和改进现有功能

## 注意事项

1. 首次运行时会自动下载NLTK数据包
2. 词向量模块默认使用简化实现，如需使用Word2Vec或BERT，请安装相应依赖
3. 网络文本收集功能需要稳定的网络连接
4. 正则表达式DFA实现了基本功能，复杂正则表达式可能需要扩展

## 快速测试

### 运行完整演示
```bash
python text_processing_system.py
```

### 单独使用模块
```python
# 导入合并文件中的类
from text_processing_system import TextCollector, CollocationFinder, BasicInvertedIndex

# 使用示例
collector = TextCollector()
texts = collector.collect_texts(source="sample")

finder = CollocationFinder()
finder.analyze_texts(texts)
```

## 项目特色

1. **完整的实现**：所有7个模块都有完整的实现，不是简单的接口调用
2. **容错性强**：当NLTK、gensim等依赖不可用时，自动切换到简化实现
3. **模块化设计**：每个模块都可以独立使用和测试
4. **实用性强**：所有功能都有实际的应用价值
5. **教学价值**：代码结构清晰，适合学习文本处理和语言处理技术

## 运行结果示例

### 文本收集
- 成功收集3篇英文新闻文本（每篇约110-113个单词）
- 文本内容涵盖经济、人工智能、气候变化等主题

### 固定搭配查找
- 找到331个二元组合、332个三元组合、329个四元组合
- 识别出高频搭配如"warn that"、"contribute to"、"decision making"等

### 反向索引搜索
- 基础索引：词汇表大小247，支持单词、短语、排序搜索
- 词法分析索引：支持词形还原和词性搜索
- 词向量索引：支持语义搜索和相似词查找

### 正则表达式DFA
- 成功构建各种正则表达式的DFA
- 支持基本操作：连接、选择、Kleene星号等

### 编程语言解释器
- 支持变量、运算、条件语句、循环等基本功能
- 能够正确执行简单的程序逻辑

## 扩展建议

1. 添加更多文本来源（如Reuters、CNN等新闻网站）
2. 实现更复杂的正则表达式支持（如字符类、量词等）
3. 集成更多预训练词向量模型（Word2Vec、GloVe、FastText）
4. 扩展编程语言解释器功能（函数定义、数组、对象等）
5. 添加图形用户界面
6. 实现分布式索引支持
7. 添加更多评估指标和性能测试

## 技术栈

- **Python 3.8+**
- **NLTK**：自然语言处理（可选，有简化实现）
- **NumPy**：数值计算
- **scikit-learn**：机器学习工具
- **BeautifulSoup4**：网页解析
- **Requests**：HTTP请求

## 作者

本项目为文本处理和语言处理技术的综合演示，展示了从基础数据结构到高级NLP技术的完整实现。所有7个核心模块已合并到单个文件 `text_processing_system.py` 中，便于使用和部署。

## 合并版本特点

- **单文件部署**：所有功能集中在一个文件中，便于分发和使用
- **完整功能**：保留了所有原始模块的完整功能
- **容错性强**：当依赖库不可用时自动切换到简化实现
- **即开即用**：运行单个文件即可体验所有功能

## 快速开始

1. 安装依赖：`pip install -r requirements.txt`
2. 运行演示：`python text_processing_system.py`
3. 查看输出，了解各模块功能

项目适合用于学习和研究文本处理技术，也可作为实际项目的基础代码。
